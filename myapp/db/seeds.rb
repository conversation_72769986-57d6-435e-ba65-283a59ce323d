# db/seeds.rb - AZMed+ Platform Seed Data
# Run with: rails db:seed
# For development reset: rails db:reset

puts "🌱 Starting AZMed+ seed data generation..."

# Clear existing data in development
if Rails.env.development?
  puts "🧹 Cleaning existing data..."
  [VideoProgress, CourseEnrollment, CourseVideo, CourseModule, Course,
   SessionParticipant, GroupSession, Prescription, Consultation, Appointment,
   DoctorAvailability, HealthMetric, PregnancyTracker, CycleTracker, HealthProfile,
   AIHealthInsight, RiskAssessment, MedicalDocument, DoctorVerification, MedicalLicense,
   OrderItem, Payment, Order, PaymentMethod, UsageTracker, UserMembership,
   TierFeature, MembershipFeature, MembershipTier, Admin, Doctor, Patient, User].each(&:destroy_all)
end

# 1. MEMBERSHIP SYSTEM
puts "💎 Creating membership tiers..."

# Membership Features
features = MembershipFeature.create!([
  { name: "Courses", feature_key: "courses", description: "Access to educational courses", feature_type: 0, category: "education" },
  { name: "Consultations", feature_key: "consultations", description: "Video consultations with doctors", feature_type: 0, category: "healthcare" },
  { name: "Emergency Support", feature_key: "emergency", description: "24/7 emergency consultations", feature_type: 1, category: "healthcare" },
  { name: "Group Sessions", feature_key: "group_sessions", description: "Access to group webinars", feature_type: 0, category: "education" },
  { name: "Recording Access", feature_key: "recordings", description: "Access to session recordings", feature_type: 2, category: "archive" },
  { name: "AI Health Insights", feature_key: "ai_insights", description: "AI-powered health recommendations", feature_type: 1, category: "ai" },
  { name: "Partner Account", feature_key: "partner", description: "Additional account for partner", feature_type: 1, category: "family" }
])

# Membership Tiers
tiers = MembershipTier.create!([
  { name: "Temel", slug: "temel", description: "Basic free tier", price: 0, currency: "TRY", billing_cycle: 1, trial_days: 0, sort_order: 1, active: true },
  { name: "Sağlık+", slug: "saglik", description: "Health focused plan", price: 299, currency: "TRY", billing_cycle: 1, trial_days: 7, sort_order: 2, active: true },
  { name: "Anne+", slug: "anne", description: "Pregnancy & reproductive health", price: 499, currency: "TRY", billing_cycle: 1, trial_days: 14, sort_order: 3, active: true },
  { name: "Premium", slug: "premium", description: "Complete healthcare solution", price: 799, currency: "TRY", billing_cycle: 1, trial_days: 14, sort_order: 4, active: true }
])

# Tier Features Configuration
tier_configs = {
  "temel" => { courses: 1, consultations: 0, emergency: false, group_sessions: 0, recordings: 7, ai_insights: false, partner: false },
  "saglik" => { courses: 3, consultations: 1, emergency: false, group_sessions: 2, recordings: 30, ai_insights: true, partner: false },
  "anne" => { courses: 5, consultations: 2, emergency: true, group_sessions: 999, recordings: 90, ai_insights: true, partner: true },
  "premium" => { courses: 8, consultations: 3, emergency: true, group_sessions: 999, recordings: 365, ai_insights: true, partner: true }
}

tier_configs.each do |tier_slug, config|
  tier = tiers.find { |t| t.slug == tier_slug }
  features.each do |feature|
    case feature.feature_key
    when "courses", "consultations", "group_sessions", "recordings"
      limit = config[feature.feature_key.to_sym]
      TierFeature.create!(
        membership_tier: tier, membership_feature: feature,
        limit_value: limit, is_unlimited: limit == 999, is_included: limit > 0
      )
    when "emergency", "ai_insights", "partner"
      included = config[feature.feature_key.to_sym]
      TierFeature.create!(
        membership_tier: tier, membership_feature: feature,
        limit_value: nil, is_unlimited: false, is_included: included
      )
    end
  end
end

# 2. USERS & AUTHENTICATION
puts "👥 Creating users..."

# Create Admin User
admin_user = User.create!(
  email: "<EMAIL>",
  password: "password123",
  first_name: "System",
  last_name: "Administrator",
  phone_number: "+905301234567",
  birth_date: 30.years.ago,
  verified_at: Time.current,
  locale: "tr",
  timezone: "Europe/Istanbul"
)

admin = Admin.create!(
  user: admin_user,
  role: "super_admin",
  permissions: { users: true, doctors: true, content: true, billing: true },
  department: "Operations"
)

# Create Dr. Ayşe Zehra ÖZDEMİR (Project Lead)
doctor_user = User.create!(
  email: "<EMAIL>",
  password: "password123",
  first_name: "Ayşe Zehra",
  last_name: "ÖZDEMİR",
  phone_number: "+905303413961",
  birth_date: Date.new(1985, 3, 15),
  verified_at: Time.current,
  locale: "tr",
  timezone: "Europe/Istanbul"
)

dr_ozdemir = Doctor.create!(
  user: doctor_user,
  license_number: "12345678901234",
  specialization: "Obstetrics & Gynecology",
  sub_specializations: ["Reproductive Endocrinology", "Maternal-Fetal Medicine"],
  graduated_university: "Ondokuz Mayıs University",
  graduation_year: 2009,
  current_institution: "Ondokuz Mayıs University Medical Faculty",
  years_experience: 15,
  consultation_fee_day: 350,
  consultation_fee_night: 500,
  consultation_fee_emergency: 750,
  languages_spoken: ["Turkish", "English"],
  bio: "Associate Professor specializing in reproductive medicine and women's health. Leading researcher in IVF protocols and pregnancy care.",
  verification_status: 2, # verified
  rating: 4.9,
  total_consultations: 1247
)

# Additional doctors
sample_doctors = [
  { name: "Fatma", surname: "YILMAZ", spec: "Gynecology", uni: "Istanbul University", exp: 12 },
  { name: "Mehmet", surname: "KAYA", spec: "Obstetrics", uni: "Ankara University", exp: 8 },
  { name: "Zeynep", surname: "DEMİR", spec: "Reproductive Medicine", uni: "Ege University", exp: 10 }
]

other_doctors = sample_doctors.map do |doc|
  user = User.create!(
    email: "#{doc[:name].downcase}.#{doc[:surname].downcase}@azmed.com",
    password: "password123",
    first_name: doc[:name],
    last_name: doc[:surname],
    phone_number: "+9053#{rand(1000000..9999999)}",
    birth_date: rand(25..45).years.ago,
    verified_at: Time.current,
    locale: "tr",
    timezone: "Europe/Istanbul"
  )
  
  Doctor.create!(
    user: user,
    license_number: rand(10**13..10**14-1).to_s,
    specialization: doc[:spec],
    graduated_university: doc[:uni],
    graduation_year: Time.current.year - doc[:exp] - 6,
    current_institution: "#{doc[:uni]} Medical Center",
    years_experience: doc[:exp],
    consultation_fee_day: rand(250..400),
    consultation_fee_night: rand(400..600),
    consultation_fee_emergency: rand(600..800),
    languages_spoken: ["Turkish"],
    verification_status: 2,
    rating: rand(4.0..5.0).round(1),
    total_consultations: rand(100..500)
  )
end

# Patient users
patient_names = [
  ["Elif", "YAMAN"], ["Seda", "KURT"], ["Ayşe", "ÇELIK"], ["Melike", "ÖZKAN"], 
  ["Gizem", "ARSLAN"], ["Burcu", "DOĞAN"], ["Ceren", "ŞAHIN"], ["Deniz", "YILDIZ"]
]

patients = patient_names.map do |first, last|
  user = User.create!(
    email: "#{first.downcase}.#{last.downcase}@example.com",
    password: "password123",
    first_name: first,
    last_name: last,
    phone_number: "+9053#{rand(1000000..9999999)}",
    birth_date: rand(18..45).years.ago,
    verified_at: Time.current,
    locale: "tr",
    timezone: "Europe/Istanbul"
  )
  
  Patient.create!(
    user: user,
    medical_record_number: "MRN#{rand(100000..999999)}",
    emergency_contact_name: "#{%w[Ahmet Mehmet Mustafa Ali].sample} #{%w[YILMAZ KAYA DEMİR ÇELİK].sample}",
    emergency_contact_phone: "+9053#{rand(1000000..9999999)}",
    blood_type: %w[A+ A- B+ B- AB+ AB- O+ O-].sample,
    pregnancy_count: rand(0..3),
    birth_count: rand(0..2),
    smoking_status: rand(0..2),
    alcohol_consumption: rand(0..2)
  )
end

# 3. MEMBERSHIPS & PAYMENTS
puts "💳 Setting up memberships and payments..."

# Assign memberships to patients
patients.each_with_index do |patient, index|
  tier = case index % 4
         when 0 then tiers[0] # Temel
         when 1 then tiers[1] # Sağlık+
         when 2 then tiers[2] # Anne+
         else tiers[3] # Premium
         end
  
  UserMembership.create!(
    user: patient.user,
    membership_tier: tier,
    status: 1, # active
    starts_at: 1.month.ago,
    expires_at: 1.month.from_now,
    auto_renewal: true
  )
  
  # Create payment method for paying customers
  if tier.price > 0
    PaymentMethod.create!(
      user: patient.user,
      gateway: "akbank",
      gateway_customer_id: "cust_#{SecureRandom.hex(8)}",
      gateway_payment_method_id: "pm_#{SecureRandom.hex(8)}",
      card_last_four: rand(1000..9999).to_s,
      card_brand: %w[visa mastercard].sample,
      expiry_month: rand(1..12),
      expiry_year: rand(2025..2030),
      is_default: true
    )
  end
end

# 4. COURSES & CONTENT
puts "📚 Creating courses and content..."

course_data = [
  {
    title: "Gebelik Öncesi Hazırlık",
    description: "Sağlıklı bir gebelik için gerekli hazırlıklar",
    category: "pregnancy",
    target_audience: 0, # patients
    price: 99,
    modules: [
      { title: "Beslenme ve Vitaminler", videos: ["Folik Asit Kullanımı", "Gebelik Öncesi Diyet"] },
      { title: "Yaşam Tarzı Değişiklikleri", videos: ["Sigara ve Alkol", "Egzersiz Programı"] }
    ]
  },
  {
    title: "Doğum Sonrası Bakım",
    description: "Lohusalık dönemi ve bebek bakımı",
    category: "postpartum",
    target_audience: 0,
    price: 149,
    modules: [
      { title: "Emzirme Rehberi", videos: ["İlk Emzirme", "Süt Üretimini Artırma"] },
      { title: "Lohusa Sağlığı", videos: ["Fiziksel İyileşme", "Postpartum Depresyon"] }
    ]
  },
  {
    title: "IVF Protokolleri",
    description: "Tüp bebek tedavisi süreçleri ve protokoller",
    category: "fertility",
    target_audience: 1, # doctors
    price: 299,
    modules: [
      { title: "Stimülasyon Protokolleri", videos: ["GnRH Agonist", "GnRH Antagonist"] },
      { title: "Embriyo Transferi", videos: ["Fresh Transfer", "Frozen Transfer"] }
    ]
  }
]

courses = course_data.map do |course_info|
  course = Course.create!(
    title: course_info[:title],
    slug: course_info[:title].parameterize,
    description: course_info[:description],
    category: course_info[:category],
    tags: [course_info[:category], "temel"],
    price: course_info[:price],
    currency: "TRY",
    duration_minutes: rand(120..300),
    difficulty_level: rand(0..2),
    target_audience: course_info[:target_audience],
    instructor: course_info[:target_audience] == 1 ? dr_ozdemir : nil,
    thumbnail_url: "https://via.placeholder.com/300x200",
    preview_video_url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    is_premium: course_info[:price] > 100,
    is_featured: [true, false].sample,
    enrollment_count: rand(50..500),
    rating: rand(4.0..5.0).round(1),
    active: true,
    published_at: rand(1..12).months.ago
  )
  
  course_info[:modules].each_with_index do |module_info, mod_index|
    mod = CourseModule.create!(
      course: course,
      title: module_info[:title],
      description: "#{module_info[:title]} hakkında detaylı bilgiler",
      sort_order: mod_index + 1,
      duration_minutes: rand(30..90)
    )
    
    module_info[:videos].each_with_index do |video_title, vid_index|
      CourseVideo.create!(
        course_module: mod,
        title: video_title,
        description: "#{video_title} konusunda ayrıntılı açıklamalar",
        video_url: "https://www.youtube.com/watch?v=#{SecureRandom.hex(5)}",
        thumbnail_url: "https://via.placeholder.com/200x120",
        duration_minutes: rand(10..30),
        sort_order: vid_index + 1,
        is_preview: vid_index == 0,
        captions_url: "https://example.com/captions/#{SecureRandom.hex(8)}.vtt",
        transcript: "Video transkripti burada yer alacak..."
      )
    end
  end
  
  course
end

# 5. APPOINTMENTS & CONSULTATIONS
puts "🩺 Creating appointments and consultations..."

# Doctor availability
[dr_ozdemir, *other_doctors].each do |doctor|
  (1..5).each do |day| # Monday to Friday
    DoctorAvailability.create!(
      doctor: doctor,
      day_of_week: day,
      start_time: "09:00",
      end_time: "17:00",
      appointment_duration: 30,
      buffer_time: 5,
      max_appointments: 16,
      is_available: true
    )
  end
end

# Sample appointments and consultations
patients.first(5).each do |patient|
  doctor = [dr_ozdemir, *other_doctors].sample
  scheduled_time = rand(1..30).days.from_now.change(hour: rand(9..16), min: [0, 30].sample)
  
  appointment = Appointment.create!(
    patient: patient,
    doctor: doctor,
    appointment_type: rand(0..2), # regular, emergency, follow_up
    status: rand(0..3), # scheduled, confirmed, completed, cancelled
    scheduled_at: scheduled_time,
    duration_minutes: 30,
    price: doctor.consultation_fee_day,
    currency: "TRY",
    location_type: 1, # online
    meeting_url: "https://meet.azmed.com/#{SecureRandom.hex(8)}",
    notes: "Rutin kontrol randevusu"
  )
  
  # Create consultation if appointment is completed
  if appointment.status == 2 # completed
    consultation = Consultation.create!(
      doctor: doctor,
      patient: patient,
      appointment: appointment,
      type: appointment.appointment_type,
      status: 2, # completed
      scheduled_at: appointment.scheduled_at,
      started_at: appointment.scheduled_at,
      ended_at: appointment.scheduled_at + 30.minutes,
      duration_minutes: 30,
      price: appointment.price,
      currency: "TRY",
      meeting_url: appointment.meeting_url,
      recording_url: "https://recordings.azmed.com/#{SecureRandom.hex(8)}.mp4",
      summary: "Hasta genel durumu iyi. Önerilen tedaviye devam edilmesi önerildi.",
      diagnosis: "Rutin kontrol - Normal bulgular",
      treatment_plan: "Mevcut tedaviye devam, 3 ay sonra kontrol",
      follow_up_required: [true, false].sample,
      follow_up_date: rand(1..3).months.from_now
    )
    
    # Create prescription if needed
    if [true, false].sample
      Prescription.create!(
        consultation: consultation,
        doctor: doctor,
        patient: patient,
        medications: [
          { name: "Folik Asit", dosage: "5mg", frequency: "Günde 1", duration: "30 gün" },
          { name: "Demir Preparatı", dosage: "80mg", frequency: "Günde 1", duration: "30 gün" }
        ],
        instructions: "İlaçları yemekten sonra alınız. Yan etki durumunda doktora başvurunuz.",
        valid_until: 30.days.from_now,
        status: 1 # active
      )
    end
  end
end

# 6. HEALTH TRACKING
puts "📊 Creating health tracking data..."

# Cycle tracking for female patients
patients.select { |p| rand(2) == 0 }.first(4).each do |patient|
  (1..6).each do |month|
    cycle_start = month.months.ago.beginning_of_month + rand(0..28).days
    
    CycleTracker.create!(
      patient: patient,
      cycle_start_date: cycle_start,
      cycle_length: rand(25..35),
      period_length: rand(3..7),
      ovulation_date: cycle_start + 14.days,
      next_period_predicted: cycle_start + rand(25..35).days,
      flow_intensity: rand(1..3),
      symptoms: ["kramplar", "şişkinlik", "baş ağrısı"].sample(rand(0..3)),
      mood: rand(1..5),
      temperature: rand(36.1..37.2).round(1),
      notes: "Normal döngü"
    )
  end
end

# Pregnancy tracking
pregnant_patient = patients.first
PregnancyTracker.create!(
  patient: pregnant_patient,
  conception_date: 20.weeks.ago,
  due_date: 20.weeks.from_now,
  current_week: 20,
  current_trimester: 2,
  weight_gain: 8.5,
  fundal_height: 20,
  fetal_heart_rate: 145,
  blood_pressure_systolic: 120,
  blood_pressure_diastolic: 80,
  glucose_level: 95,
  iron_level: 12.5,
  symptoms: ["hafif bulantı", "sırt ağrısı"],
  notes: "Gebelik normal seyrinde",
  risk_factors: [],
  active: true
)

# 7. GROUP SESSIONS
puts "👥 Creating group sessions..."

group_sessions = [
  { title: "Gebelik Yoga Sınıfı", type: 0, max: 15, price: 75, category: "wellness" },
  { title: "Emzirme Destek Grubu", type: 1, max: 20, price: 0, category: "support" },
  { title: "Doğuma Hazırlık Webinarı", type: 2, max: 100, price: 150, category: "education" }
].map do |session_info|
  session = GroupSession.create!(
    title: session_info[:title],
    description: "#{session_info[:title]} - Uzman eşliğinde interaktif oturum",
    session_type: session_info[:type],
    max_participants: session_info[:max],
    current_participants: rand(5..session_info[:max]-5),
    price: session_info[:price],
    currency: "TRY",
    host: dr_ozdemir,
    scheduled_at: rand(1..14).days.from_now.change(hour: rand(10..16)),
    duration_minutes: rand(60..120),
    meeting_url: "https://groups.azmed.com/#{SecureRandom.hex(8)}",
    status: rand(0..2), # scheduled, live, completed
    category: session_info[:category],
    tags: [session_info[:category]]
  )
  
  # Add participants
  participants_count = rand(3..8)
  patients.sample(participants_count).each do |patient|
    SessionParticipant.create!(
      group_session: session,
      user: patient.user,
      role: 0, # participant
      payment_status: session.price > 0 ? rand(0..1) : 1 # paid or free
    )
  end
  
  session
end

# 8. AI INSIGHTS & ANALYTICS
puts "🤖 Generating AI insights..."

patients.first(3).each do |patient|
  # Health insights
  AIHealthInsight.create!(
    patient: patient,
    insight_type: "cycle_prediction",
    confidence_score: rand(0.7..0.95).round(2),
    recommendation: "Döngünüz düzenli seyrediyor. Ovulasyon tahmini doğruluk oranı %#{(rand(85..95))}",
    data_points: { avg_cycle: 28, variance: 2, last_3_cycles: [28, 29, 27] },
    generated_at: Time.current
  )
  
  # Risk assessment
  RiskAssessment.create!(
    patient: patient,
    assessment_type: "general_health",
    risk_level: rand(0..2), # low, medium, high
    risk_factors: ["yaş", "aile hikayesi"].sample(rand(0..2)),
    recommendations: "Düzenli kontroller ve sağlıklı yaşam tarzı önerilir",
    calculated_at: Time.current,
    expires_at: 6.months.from_now
  )
end

# 9. NOTIFICATIONS
puts "📨 Setting up notifications..."

# Sample notification templates
NotificationTemplate.create!([
  {
    name: "appointment_reminder",
    subject: "Randevu Hatırlatması",
    body_template: "Merhaba {{user_name}}, {{appointment_date}} tarihinde {{doctor_name}} ile randevunuz bulunmaktadır.",
    notification_type: "appointment",
    variables: ["user_name", "appointment_date", "doctor_name"],
    active: true
  },
  {
    name: "course_completion",
    subject: "Kurs Tamamlandı",
    body_template: "Tebrikler {{user_name}}! {{course_name}} kursunu başarıyla tamamladınız.",
    notification_type: "education",
    variables: ["user_name", "course_name"],
    active: true
  }
])

# Sample notifications
patients.first(3).each do |patient|
  Notification.create!(
    user: patient.user,
    title: "Hoş Geldiniz!",
    body: "AZMed+ platformuna hoş geldiniz. Sağlık yolculuğunuzda size eşlik etmekten mutluluk duyuyoruz.",
    notification_type: "welcome",
    data: { source: "system" },
    sent_at: Time.current
  )
end

# 10. USAGE TRACKING
puts "📈 Initializing usage tracking...")

# Initialize current month usage for all users with memberships
UserMembership.includes(:user, :membership_tier).each do |membership|
  features.each do |feature|
    tier_feature = membership.membership_tier.tier_features.find_by(membership_feature: feature)
    next unless tier_feature&.is_included
    
    current_usage = case feature.feature_key
                   when "courses" then rand(0..tier_feature.limit_value) if tier_feature.limit_value
                   when "consultations" then rand(0..tier_feature.limit_value) if tier_feature.limit_value
                   else 0
                   end
    
    if current_usage
      UsageTracker.create!(
        user: membership.user,
        membership_feature: feature,
        period_start: Date.current.beginning_of_month,
        period_end: Date.current.end_of_month,
        usage_count: current_usage,
        limit_exceeded: tier_feature.limit_value && current_usage >= tier_feature.limit_value
      )
    end
  end
end

puts "✅ Seed data generation completed successfully!"
puts ""
puts "📊 Generated:"
puts "   👥 Users: #{User.count} (#{Admin.count} admins, #{Doctor.count} doctors, #{Patient.count} patients)"
puts "   💎 Membership Tiers: #{MembershipTier.count} with #{MembershipFeature.count} features"
puts "   🩺 Consultations: #{Consultation.count} with #{Prescription.count} prescriptions"
puts "   📚 Courses: #{Course.count} with #{CourseVideo.count} videos"
puts "   👥 Group Sessions: #{GroupSession.count} with #{SessionParticipant.count} participants"
puts "   📅 Appointments: #{Appointment.count}"
puts "   🤖 AI Insights: #{AIHealthInsight.count} insights, #{RiskAssessment.count} assessments"
puts ""
puts "🔑 Test Credentials:"
puts "   Admin: <EMAIL> / password123"
puts "   Doctor: <EMAIL> / password123"
puts "   Patient: <EMAIL> / password123"
puts ""
puts "🚀 Your AZMed+ platform is ready for development!"