# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_06_20_125518) do
  create_table "admins", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "role", default: "admin", null: false
    t.text "permissions"
    t.string "department", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["role"], name: "index_admins_on_role"
    t.index ["user_id"], name: "index_admins_on_user_id"
  end

  create_table "doctors", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "license_number", null: false
    t.string "specialization", null: false
    t.text "sub_specializations"
    t.string "graduated_university", null: false
    t.integer "graduation_year", null: false
    t.string "current_institution"
    t.integer "years_experience", default: 0
    t.decimal "consultation_fee_day", precision: 8, scale: 2, null: false
    t.decimal "consultation_fee_night", precision: 8, scale: 2, null: false
    t.decimal "consultation_fee_emergency", precision: 8, scale: 2, null: false
    t.text "languages_spoken"
    t.text "bio"
    t.integer "verification_status", default: 0
    t.decimal "rating", precision: 3, scale: 2
    t.integer "total_consultations", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["license_number"], name: "index_doctors_on_license_number", unique: true
    t.index ["rating"], name: "index_doctors_on_rating"
    t.index ["specialization"], name: "index_doctors_on_specialization"
    t.index ["user_id"], name: "index_doctors_on_user_id"
    t.index ["verification_status"], name: "index_doctors_on_verification_status"
  end

  create_table "patients", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "medical_record_number", null: false
    t.string "emergency_contact_name", null: false
    t.string "emergency_contact_phone", null: false
    t.string "blood_type"
    t.integer "pregnancy_count", default: 0
    t.integer "birth_count", default: 0
    t.integer "smoking_status", default: 0
    t.integer "alcohol_consumption", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["blood_type"], name: "index_patients_on_blood_type"
    t.index ["medical_record_number"], name: "index_patients_on_medical_record_number", unique: true
    t.index ["user_id"], name: "index_patients_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", null: false
    t.string "password_digest", null: false
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.string "phone_number", null: false
    t.date "birth_date", null: false
    t.datetime "verified_at"
    t.string "locale", default: "tr"
    t.string "timezone", default: "Europe/Istanbul"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["phone_number"], name: "index_users_on_phone_number"
    t.index ["verified_at"], name: "index_users_on_verified_at"
  end

  add_foreign_key "admins", "users"
  add_foreign_key "doctors", "users"
  add_foreign_key "patients", "users"
end
