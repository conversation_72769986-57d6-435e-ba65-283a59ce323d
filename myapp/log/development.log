Started GET "/" for 127.0.0.1 at 2025-06-20 15:46:13 +0300
  [1m[35m (3.8ms)[0m  [1m[35mCREATE TABLE "schema_migrations" ("version" varchar NOT NULL PRIMARY KEY) /*application='Myapp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE TABLE "ar_internal_metadata" ("key" varchar NOT NULL PRIMARY KEY, "value" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.3ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
Processing by Rails::WelcomeController#index as HTML
  Rendering /home/<USER>/.rbenv/versions/3.3.0/lib/ruby/gems/3.3.0/gems/railties-8.0.2/lib/rails/templates/rails/welcome/index.html.erb
  Rendered /home/<USER>/.rbenv/versions/3.3.0/lib/ruby/gems/3.3.0/gems/railties-8.0.2/lib/rails/templates/rails/welcome/index.html.erb (Duration: 0.7ms | GC: 0.0ms)
Completed 200 OK in 22ms (Views: 5.5ms | ActiveRecord: 0.0ms (0 queries, 0 cached) | GC: 0.7ms)


Started GET "/icon.png" for 127.0.0.1 at 2025-06-20 15:46:14 +0300
  
ActionController::RoutingError (No route matches [GET] "/icon.png"):
  
Started GET "/icon.png" for 127.0.0.1 at 2025-06-20 15:46:14 +0300
  
ActionController::RoutingError (No route matches [GET] "/icon.png"):
  
