# AZMed+ Enhanced Backend Architecture - yUML

## Core User Management
[User|+id:UUID;+email:string;+password_digest:string;+first_name:string;+last_name:string;+phone_number:string;+birth_date:date;+profile_photo:string;+locale:string;+timezone:string;+verified_at:timestamp;+last_login_at:timestamp;+created_at:timestamp;+updated_at:timestamp|+authenticate(password);+generate_jwt_token();+verify_account();+update_profile()]

[Patient|+id:UUID;+user_id:UUID;+medical_record_number:string;+emergency_contact_name:string;+emergency_contact_phone:string;+blood_type:string;+allergies:text;+current_medications:text;+medical_conditions:text;+pregnancy_count:integer;+birth_count:integer;+smoking_status:integer;+alcohol_consumption:integer;+created_at:timestamp;+updated_at:timestamp|+update_medical_history();+get_health_summary();+export_medical_data()]

[Doctor|+id:UUID;+user_id:UUID;+license_number:string;+specialization:string;+sub_specializations:jsonb;+graduated_university:string;+graduation_year:integer;+current_institution:string;+years_experience:integer;+consultation_fee_day:decimal;+consultation_fee_night:decimal;+consultation_fee_emergency:decimal;+languages_spoken:jsonb;+bio:text;+verification_status:integer;+rating:decimal;+total_consultations:integer;+created_at:timestamp;+updated_at:timestamp|+set_availability();+update_pricing();+get_patient_history();+write_prescription()]

[Admin|+id:UUID;+user_id:UUID;+role:string;+permissions:jsonb;+department:string;+created_at:timestamp;+updated_at:timestamp|+manage_users();+verify_doctors();+monitor_system()]

## Professional Verification System
[DoctorVerification|+id:UUID;+doctor_id:UUID;+document_type:string;+document_url:string;+document_hash:string;+verification_status:integer;+submitted_at:timestamp;+reviewed_at:timestamp;+reviewed_by:UUID;+rejection_reason:text;+expiry_date:date;+created_at:timestamp;+updated_at:timestamp|+submit_document();+approve();+reject();+schedule_renewal()]

[MedicalLicense|+id:UUID;+doctor_id:UUID;+license_number:string;+issuing_authority:string;+issue_date:date;+expiry_date:date;+status:integer;+created_at:timestamp;+updated_at:timestamp]

## Enhanced Membership & Billing System
[MembershipTier|+id:UUID;+name:string;+slug:string;+description:text;+price:decimal;+currency:string;+billing_cycle:integer;+trial_days:integer;+sort_order:integer;+active:boolean;+created_at:timestamp;+updated_at:timestamp|+calculate_prorated_amount();+get_features()]

[MembershipFeature|+id:UUID;+name:string;+feature_key:string;+description:text;+feature_type:integer;+category:string;+created_at:timestamp;+updated_at:timestamp]

[TierFeature|+id:UUID;+membership_tier_id:UUID;+membership_feature_id:UUID;+limit_value:integer;+is_unlimited:boolean;+is_included:boolean;+created_at:timestamp;+updated_at:timestamp]

[UserMembership|+id:UUID;+user_id:UUID;+membership_tier_id:UUID;+status:integer;+starts_at:timestamp;+expires_at:timestamp;+cancelled_at:timestamp;+auto_renewal:boolean;+payment_method_id:UUID;+created_at:timestamp;+updated_at:timestamp|+renew();+cancel();+upgrade();+downgrade()]

[UsageTracker|+id:UUID;+user_id:UUID;+membership_feature_id:UUID;+period_start:date;+period_end:date;+usage_count:integer;+limit_exceeded:boolean;+created_at:timestamp;+updated_at:timestamp|+increment_usage();+check_limit();+reset_monthly()]

## Payment & Order Management
[PaymentMethod|+id:UUID;+user_id:UUID;+gateway:string;+gateway_customer_id:string;+gateway_payment_method_id:string;+card_last_four:string;+card_brand:string;+expiry_month:integer;+expiry_year:integer;+is_default:boolean;+created_at:timestamp;+updated_at:timestamp]

[Order|+id:UUID;+user_id:UUID;+order_number:string;+total_amount:decimal;+tax_amount:decimal;+discount_amount:decimal;+currency:string;+status:integer;+order_type:integer;+metadata:jsonb;+created_at:timestamp;+updated_at:timestamp]

[Payment|+id:UUID;+order_id:UUID;+payment_method_id:UUID;+amount:decimal;+currency:string;+gateway:string;+gateway_transaction_id:string;+gateway_response:jsonb;+status:integer;+paid_at:timestamp;+created_at:timestamp;+updated_at:timestamp]

[OrderItem|+id:UUID;+order_id:UUID;+purchasable_type:string;+purchasable_id:UUID;+quantity:integer;+unit_price:decimal;+total_price:decimal;+created_at:timestamp;+updated_at:timestamp]

## Healthcare Services
[Consultation|+id:UUID;+doctor_id:UUID;+patient_id:UUID;+appointment_id:UUID;+type:integer;+status:integer;+scheduled_at:timestamp;+started_at:timestamp;+ended_at:timestamp;+duration_minutes:integer;+price:decimal;+currency:string;+meeting_url:string;+recording_url:string;+summary:text;+diagnosis:text;+treatment_plan:text;+follow_up_required:boolean;+follow_up_date:timestamp;+created_at:timestamp;+updated_at:timestamp|+start_session();+end_session();+generate_summary();+schedule_follow_up()]

[Prescription|+id:UUID;+consultation_id:UUID;+doctor_id:UUID;+patient_id:UUID;+medications:jsonb;+instructions:text;+valid_until:date;+status:integer;+created_at:timestamp;+updated_at:timestamp]

[Course|+id:UUID;+title:string;+slug:string;+description:text;+objectives:text;+category:string;+tags:jsonb;+price:decimal;+currency:string;+duration_minutes:integer;+difficulty_level:integer;+target_audience:integer;+instructor_id:UUID;+thumbnail_url:string;+preview_video_url:string;+is_premium:boolean;+is_featured:boolean;+enrollment_count:integer;+rating:decimal;+active:boolean;+published_at:timestamp;+created_at:timestamp;+updated_at:timestamp]

[CourseModule|+id:UUID;+course_id:UUID;+title:string;+description:text;+sort_order:integer;+duration_minutes:integer;+created_at:timestamp;+updated_at:timestamp]

[CourseVideo|+id:UUID;+course_module_id:UUID;+title:string;+description:text;+video_url:string;+thumbnail_url:string;+duration_minutes:integer;+sort_order:integer;+is_preview:boolean;+video_quality:jsonb;+captions_url:string;+transcript:text;+created_at:timestamp;+updated_at:timestamp]

[CourseEnrollment|+id:UUID;+course_id:UUID;+user_id:UUID;+progress_percentage:integer;+completed_at:timestamp;+certificate_url:string;+status:integer;+enrolled_at:timestamp;+created_at:timestamp;+updated_at:timestamp|+calculate_progress();+generate_certificate();+track_completion()]

[VideoProgress|+id:UUID;+course_video_id:UUID;+user_id:UUID;+watched_duration:integer;+total_duration:integer;+completion_percentage:integer;+completed:boolean;+last_position:integer;+last_watched_at:timestamp;+created_at:timestamp;+updated_at:timestamp]

## Group Sessions & Webinars
[GroupSession|+id:UUID;+title:string;+description:text;+session_type:integer;+max_participants:integer;+current_participants:integer;+price:decimal;+currency:string;+host_id:UUID;+scheduled_at:timestamp;+duration_minutes:integer;+meeting_url:string;+recording_url:string;+status:integer;+category:string;+tags:jsonb;+created_at:timestamp;+updated_at:timestamp]

[SessionParticipant|+id:UUID;+group_session_id:UUID;+user_id:UUID;+role:integer;+joined_at:timestamp;+left_at:timestamp;+payment_status:integer;+created_at:timestamp;+updated_at:timestamp]

## Health Tracking & Monitoring
[HealthProfile|+id:UUID;+patient_id:UUID;+height:decimal;+weight:decimal;+bmi:decimal;+blood_type:string;+chronic_conditions:jsonb;+family_history:jsonb;+lifestyle_factors:jsonb;+created_at:timestamp;+updated_at:timestamp]

[CycleTracker|+id:UUID;+patient_id:UUID;+cycle_start_date:date;+cycle_length:integer;+period_length:integer;+ovulation_date:date;+next_period_predicted:date;+flow_intensity:integer;+symptoms:jsonb;+mood:integer;+temperature:decimal;+notes:text;+created_at:timestamp;+updated_at:timestamp]

[PregnancyTracker|+id:UUID;+patient_id:UUID;+conception_date:date;+due_date:date;+current_week:integer;+current_trimester:integer;+weight_gain:decimal;+fundal_height:decimal;+fetal_heart_rate:integer;+blood_pressure_systolic:integer;+blood_pressure_diastolic:integer;+glucose_level:decimal;+iron_level:decimal;+symptoms:jsonb;+notes:text;+risk_factors:jsonb;+active:boolean;+created_at:timestamp;+updated_at:timestamp]

[HealthMetric|+id:UUID;+patient_id:UUID;+trackable_type:string;+trackable_id:UUID;+metric_type:string;+value:decimal;+unit:string;+recorded_at:timestamp;+source:string;+notes:text;+created_at:timestamp;+updated_at:timestamp]

## Calendar & Appointment Management
[Appointment|+id:UUID;+patient_id:UUID;+doctor_id:UUID;+appointment_type:integer;+status:integer;+scheduled_at:timestamp;+duration_minutes:integer;+price:decimal;+currency:string;+location_type:integer;+meeting_url:string;+notes:text;+reminder_sent:boolean;+cancelled_at:timestamp;+cancellation_reason:text;+created_at:timestamp;+updated_at:timestamp]

[DoctorAvailability|+id:UUID;+doctor_id:UUID;+day_of_week:integer;+start_time:time;+end_time:time;+appointment_duration:integer;+buffer_time:integer;+max_appointments:integer;+is_available:boolean;+created_at:timestamp;+updated_at:timestamp]

[CalendarIntegration|+id:UUID;+user_id:UUID;+provider:string;+external_calendar_id:string;+access_token:string;+refresh_token:string;+sync_enabled:boolean;+last_synced_at:timestamp;+created_at:timestamp;+updated_at:timestamp]

## Notifications & Communications
[Notification|+id:UUID;+user_id:UUID;+title:string;+body:text;+notification_type:string;+data:jsonb;+read_at:timestamp;+sent_at:timestamp;+delivery_method:integer;+created_at:timestamp;+updated_at:timestamp]

[NotificationTemplate|+id:UUID;+name:string;+subject:string;+body_template:text;+notification_type:string;+variables:jsonb;+active:boolean;+created_at:timestamp;+updated_at:timestamp]

[EmailQueue|+id:UUID;+recipient_email:string;+subject:string;+body:text;+template_id:UUID;+status:integer;+sent_at:timestamp;+retry_count:integer;+error_message:text;+created_at:timestamp;+updated_at:timestamp]

[SMSQueue|+id:UUID;+recipient_phone:string;+message:text;+status:integer;+sent_at:timestamp;+retry_count:integer;+error_message:text;+created_at:timestamp;+updated_at:timestamp]

## AI & Analytics
[AIHealthInsight|+id:UUID;+patient_id:UUID;+insight_type:string;+confidence_score:decimal;+recommendation:text;+data_points:jsonb;+generated_at:timestamp;+acknowledged_at:timestamp;+created_at:timestamp;+updated_at:timestamp]

[RiskAssessment|+id:UUID;+patient_id:UUID;+assessment_type:string;+risk_level:integer;+risk_factors:jsonb;+recommendations:text;+calculated_at:timestamp;+expires_at:timestamp;+created_at:timestamp;+updated_at:timestamp]

[SystemAnalytics|+id:UUID;+metric_name:string;+metric_value:decimal;+dimensions:jsonb;+recorded_at:timestamp;+created_at:timestamp;+updated_at:timestamp]

## File Management
[Attachment|+id:UUID;+attachable_type:string;+attachable_id:UUID;+file_name:string;+file_type:string;+file_size:integer;+file_url:string;+file_hash:string;+uploaded_by:UUID;+created_at:timestamp;+updated_at:timestamp]

[MedicalDocument|+id:UUID;+patient_id:UUID;+document_type:string;+title:string;+description:text;+file_url:string;+issued_by:UUID;+issued_at:timestamp;+expires_at:timestamp;+status:integer;+created_at:timestamp;+updated_at:timestamp]

## Core Relationships
[User]1-0..1>[Patient]
[User]1-0..1>[Doctor]
[User]1-0..1>[Admin]
[Doctor]1-*>[DoctorVerification]
[Doctor]1-*>[MedicalLicense]
[Doctor]1-*>[DoctorAvailability]

## Membership Relationships
[User]1-*>[UserMembership]
[MembershipTier]1-*>[UserMembership]
[MembershipTier]1-*>[TierFeature]
[MembershipFeature]1-*>[TierFeature]
[User]1-*>[UsageTracker]
[MembershipFeature]1-*>[UsageTracker]

## Payment Relationships
[User]1-*>[PaymentMethod]
[User]1-*>[Order]
[Order]1-*>[Payment]
[Order]1-*>[OrderItem]
[PaymentMethod]1-*>[Payment]

## Healthcare Service Relationships
[Doctor]1-*>[Consultation]
[Patient]1-*>[Consultation]
[Consultation]1-0..1>[Prescription]
[Doctor]1-*>[Course]
[Course]1-*>[CourseModule]
[CourseModule]1-*>[CourseVideo]
[Course]1-*>[CourseEnrollment]
[User]1-*>[CourseEnrollment]
[CourseVideo]1-*>[VideoProgress]
[User]1-*>[VideoProgress]

## Group Session Relationships
[Doctor]1-*>[GroupSession]
[GroupSession]1-*>[SessionParticipant]
[User]1-*>[SessionParticipant]

## Health Tracking Relationships
[Patient]1-0..1>[HealthProfile]
[Patient]1-*>[CycleTracker]
[Patient]1-*>[PregnancyTracker]
[Patient]1-*>[HealthMetric]
[Patient]1-*>[AIHealthInsight]
[Patient]1-*>[RiskAssessment]

## Appointment Relationships
[Patient]1-*>[Appointment]
[Doctor]1-*>[Appointment]
[Appointment]0..1-0..1>[Consultation]

## Communication Relationships
[User]1-*>[Notification]
[User]1-*>[CalendarIntegration]

## Document Relationships
[Patient]1-*>[MedicalDocument]
[User]1-*>[Attachment]

## Polymorphic Relationships
[OrderItem]-.->[UserMembership]
[OrderItem]-.->[Course]
[OrderItem]-.->[Consultation]
[OrderItem]-.->[GroupSession]
[HealthMetric]-.->[CycleTracker]
[HealthMetric]-.->[PregnancyTracker]
[Attachment]-.->[User]
[Attachment]-.->[Consultation]
[Attachment]-.->[MedicalDocument]

## Notes and Annotations
[note: Rails 7+ API with UUID primary keys {bg:lightblue}]
[note: JWT authentication with refresh tokens {bg:orange}]
[note: Polymorphic associations for flexible data modeling {bg:wheat}]
[note: Enum fields: status, types, verification_status {bg:lightgreen}]
[note: HIPAA compliant data encryption at rest and in transit {bg:lightyellow}]
[note: Multi-tenant architecture with row-level security {bg:lavender}]
[note: Real-time features via Action Cable WebSockets {bg:lightcoral}]
[note: Background jobs via Sidekiq for async processing {bg:lightcyan}]

[User]-[note: Central user entity with role-based access {bg:cornsilk}]
[Patient]-[note: Extended medical profile with tracking capabilities {bg:lightgreen}]
[Doctor]-[note: Professional verification required for platform access {bg:lightyellow}]
[UserMembership]-[note: Flexible tier system with usage limits {bg:lightblue}]
[Consultation]-[note: Video consultations with recording and AI analysis {bg:orange}]
[Course]-[note: Structured learning with progress tracking {bg:wheat}]